import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import bcrypt from "bcryptjs"; // 计算输入密码对应的哈希值

// 用户登录
export async function POST(req: Request) {
  const { userId, password } = await req.json();

  // 查找用户
  const user = await prisma.user.findUnique({
    where: { userId },
  });

  if (!user) {
    return NextResponse.json({ error: "用户不存在" }, { status: 400 });
  }

  // 比对密码
  const isPasswordValid = await bcrypt.compare(
    password,
    user.passwordHash || ""
  );

  if (!isPasswordValid) {
    return NextResponse.json(
      { message: "密码错误", userId: user.userId },
      { status: 400 }
    );
  }

  return NextResponse.json({ message: "登录成功", userId: user.userId });
}

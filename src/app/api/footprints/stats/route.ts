import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 足迹点类型定义
interface FootprintPoint {
  latitude: number;
  longitude: number;
  timestamp: string;
}

// 计算两点之间的距离（使用 Haversine 公式，返回公里）
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371; // 地球半径（公里）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

// 计算足迹记录的总距离
function calculateTotalDistance(footPrints: FootprintPoint[]): number {
  if (footPrints.length < 2) return 0;
  
  let totalDistance = 0;
  for (let i = 1; i < footPrints.length; i++) {
    const prev = footPrints[i - 1];
    const curr = footPrints[i];
    totalDistance += calculateDistance(prev.latitude, prev.longitude, curr.latitude, curr.longitude);
  }
  return totalDistance;
}

// 计算足迹记录的总时长（小时）
function calculateTotalDuration(footPrints: FootprintPoint[]): number {
  if (footPrints.length < 2) return 0;
  
  const startTime = new Date(footPrints[0].timestamp);
  const endTime = new Date(footPrints[footPrints.length - 1].timestamp);
  const durationMs = endTime.getTime() - startTime.getTime();
  return durationMs / (1000 * 60 * 60); // 转换为小时
}

// 查询用户足迹统计
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const activityType = searchParams.get("activityType");

    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    // 验证用户存在
    const user = await prisma.user.findUnique({
      where: { userId },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 构建查询条件
    const whereConditions: any = {
      userId,
    };

    // 添加时间范围过滤
    if (startDate || endDate) {
      whereConditions.createdAt = {};
      if (startDate) {
        whereConditions.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        whereConditions.createdAt.lte = new Date(endDate);
      }
    }

    // 添加活动类型过滤
    if (activityType) {
      const validActivityTypes = ["walking", "cycling", "bus", "subway"];
      if (!validActivityTypes.includes(activityType)) {
        return NextResponse.json(
          { error: "无效的activityType" },
          { status: 400 }
        );
      }
      whereConditions.activityType = activityType;
    }

    // 查询足迹记录
    const footprints = await prisma.userFootprints.findMany({
      where: whereConditions,
    });

    // 计算统计数据
    const totalTrips = footprints.length;
    const finishedTrips = footprints.filter(fp => fp.isFinished).length;
    const ongoingTrips = footprints.filter(fp => !fp.isFinished).length;

    let totalDistance = 0;
    let totalDuration = 0;

    // 按活动类型分组统计
    const activityStats: { [key: string]: { trips: number; distance: number; duration: number } } = {
      walking: { trips: 0, distance: 0, duration: 0 },
      cycling: { trips: 0, distance: 0, duration: 0 },
      bus: { trips: 0, distance: 0, duration: 0 },
      subway: { trips: 0, distance: 0, duration: 0 },
    };

    footprints.forEach(footprint => {
      const points = (footprint.footPrints as unknown) as FootprintPoint[];
      const distance = calculateTotalDistance(points);
      const duration = calculateTotalDuration(points);

      totalDistance += distance;
      totalDuration += duration;

      // 更新活动类型统计
      if (activityStats[footprint.activityType]) {
        activityStats[footprint.activityType].trips += 1;
        activityStats[footprint.activityType].distance += distance;
        activityStats[footprint.activityType].duration += duration;
      }
    });

    const stats = {
      totalTrips,
      finishedTrips,
      ongoingTrips,
      totalDistance: Math.round(totalDistance * 100) / 100, // 保留两位小数
      totalDuration: Math.round(totalDuration * 100) / 100, // 保留两位小数
      activityStats: Object.entries(activityStats).reduce((acc, [key, value]) => {
        acc[key] = {
          trips: value.trips,
          distance: Math.round(value.distance * 100) / 100,
          duration: Math.round(value.duration * 100) / 100,
        };
        return acc;
      }, {} as { [key: string]: { trips: number; distance: number; duration: number } }),
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error("查询足迹统计失败:", error);
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    );
  }
}

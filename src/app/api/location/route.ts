import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 获取用户的经纬度位置
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");

    if (!userId) {
      return NextResponse.json({ error: "缺少 userId 参数" }, { status: 400 });
    }

    // 查找用户位置
    const location = await prisma.userLocation.findUnique({
      where: { userId },
      select: {
        userId: true,
        latitude: true,
        longitude: true,
      },
    });

    if (!location) {
      return NextResponse.json({ error: "用户位置不存在" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: location,
    });
  } catch (error) {
    console.error("获取用户位置失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// 更新用户的位置
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { userId, latitude, longitude } = body;

    // 1. 验证输入数据
    if (!userId || latitude === undefined || longitude === undefined) {
      return NextResponse.json(
        { message: "Missing required fields: userId, latitude, or longitude." },
        { status: 400 }
      );
    }

    // 2. 更新 UserLocation 和 User 表
    await prisma.$transaction([
      // 使用 upsert 操作更新 UserLocation 表（如果存在就update，否则insert）
      prisma.userLocation.upsert({
        where: { userId: userId },
        update: {
          latitude: latitude,
          longitude: longitude,
          lastUpdate: new Date(),
          lastOnlineTime: new Date(), // 每次更新位置，也更新在线时间
        },
        create: {
          userId: userId,
          latitude: latitude,
          longitude: longitude,
          lastUpdate: new Date(),
          lastOnlineTime: new Date(),
        },
      }),

      // 同时更新 User 表的 lastActiveTime 字段
      prisma.user.update({
        where: { userId: userId },
        data: {
          lastActiveTime: new Date(),
        },
      }),
    ]);

    return NextResponse.json({
      success: true,
      message: "Location updated successfully.",
    });
  } catch (error) {
    console.error("Failed to update location:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error." },
      { status: 500 }
    );
  }
}

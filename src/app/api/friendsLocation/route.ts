import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 批量获取好友位置信息
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { friendIds } = body;

    // 验证输入参数
    if (!friendIds || !Array.isArray(friendIds) || friendIds.length === 0) {
      return NextResponse.json(
        { error: "缺少 friendIds 参数或参数格式错误" },
        { status: 400 }
      );
    }

    // 查询开启位置共享的好友信息及其位置
    const friendsWithLocation = await prisma.user.findMany({
      where: {
        userId: { in: friendIds },
        sharingLocation: true, // 只查询开启位置共享的用户
      },
      select: {
        userId: true,
        nickname: true,
        avatarURL: true,
        lastActiveTime: true,
        locations: {
          select: {
            latitude: true,
            longitude: true,
            lastUpdate: true,
            lastOnlineTime: true,
          },
        },
      },
    });

    // 格式化返回数据，只包含有位置信息的好友
    const friends = friendsWithLocation
      .filter((friend) => friend.locations.length > 0) // 确保有位置信息
      .map((friend) => {
        const location = friend.locations[0]; // 每个用户只有一条位置记录
        return {
          userId: friend.userId,
          nickname: friend.nickname,
          avatarURL: friend.avatarURL,
          lastActiveTime: friend.lastActiveTime,
          latitude: location.latitude,
          longitude: location.longitude,
          lastUpdate: location.lastUpdate,
          lastOnlineTime: location.lastOnlineTime,
        };
      });

    return NextResponse.json({
      success: true,
      data: {
        friends: friends,
        total: friends.length,
      },
    });
  } catch (error) {
    console.error("批量获取好友位置失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
